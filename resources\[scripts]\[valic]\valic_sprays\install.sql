-- Tabulka pro ulo<PERSON>ení sprayů
CREATE TABLE IF NOT EXISTS `valic_sprays` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `coords` longtext NOT NULL COMMENT 'JSON souřadnice sprayu',
    `rotation` longtext NOT NULL COMMENT 'JSON rotace sprayu',
    `text` varchar(255) NOT NULL COMMENT 'Text sprayu',
    `color` longtext NOT NULL COMMENT 'JSON barva sprayu',
    `creator` varchar(50) NOT NULL COMMENT 'Citizen ID tvůrce',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `expires_at` timestamp NOT NULL COMMENT 'Čas expirace sprayu',
    `size` float NOT NULL DEFAULT 1.0 COMMENT 'Velikost sprayu',
    `font` int(11) NOT NULL DEFAULT 4 COMMENT 'Font sprayu',
    PRIMARY KEY (`id`),
    <PERSON>EY `creator` (`creator`),
    <PERSON>EY `expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Migrace pro existující tabulku (pokud už existuje)
ALTER TABLE `valic_sprays`
ADD COLUMN IF NOT EXISTS `text` varchar(255) NOT NULL DEFAULT '' COMMENT 'Text sprayu' AFTER `rotation`,
ADD COLUMN IF NOT EXISTS `size` float NOT NULL DEFAULT 1.0 COMMENT 'Velikost sprayu' AFTER `expires_at`,
ADD COLUMN IF NOT EXISTS `font` int(11) NOT NULL DEFAULT 4 COMMENT 'Font sprayu' AFTER `size`,
CHANGE COLUMN `pattern` `pattern` varchar(50) NULL COMMENT 'Vzor sprayu (deprecated)';

-- Tabulka pro statistiky sprayů (volitelné)
CREATE TABLE IF NOT EXISTS `valic_spray_stats` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `citizenid` varchar(50) NOT NULL,
    `total_sprays` int(11) NOT NULL DEFAULT 0,
    `sprays_removed` int(11) NOT NULL DEFAULT 0,
    `last_spray` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `citizenid` (`citizenid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Tabulka pro reporty sprayů (volitelné)
CREATE TABLE IF NOT EXISTS `valic_spray_reports` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `spray_id` int(11) NOT NULL,
    `reporter_citizenid` varchar(50) NOT NULL,
    `reason` varchar(255) NOT NULL,
    `status` enum('pending','reviewed','resolved') NOT NULL DEFAULT 'pending',
    `admin_notes` text DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `spray_id` (`spray_id`),
    KEY `reporter_citizenid` (`reporter_citizenid`),
    KEY `status` (`status`),
    FOREIGN KEY (`spray_id`) REFERENCES `valic_sprays` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
