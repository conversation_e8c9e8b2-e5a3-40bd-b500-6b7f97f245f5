local QBCore = exports['qb-core']:GetCoreObject()

-- Lokální proměnné
local sprays = {}
local isSprayMenuOpen = false
local currentSprayItem = nil

-- Načtení sprayů při připojení
CreateThread(function()
    while not QBCore.Functions.GetPlayerData().citizenid do
        Wait(100)
    end
    
    QBCore.Functions.TriggerCallback('valic_sprays:server:getSprays', function(serverSprays)
        sprays = serverSprays
        for sprayId, sprayData in pairs(sprays) do
            createSprayObject(sprayId, sprayData)
        end
    end)
end)

-- Funkce pro kontrolu zakázaných zón
local function isInRestrictedZone(coords)
    for _, zone in pairs(Config.RestrictedZones) do
        local distance = #(coords - zone.coords)
        if distance <= zone.radius then
            return true, zone.name
        end
    end
    return false
end

-- Funkce pro kontrolu povrchu
local function isValidSurface(coords)
    local hit, _, _, _, materialHash = GetShapeTestResult(StartShapeTestRay(coords.x, coords.y, coords.z + 1.0, coords.x, coords.y, coords.z - 1.0, -1, 0, 7))
    
    if hit then
        return Config.AllowedSurfaces[materialHash] or false
    end
    return false
end

-- Funkce pro vytvoření spray objektu
function createSprayObject(sprayId, sprayData)
    -- Zde by se vytvořil 3D objekt sprayu
    -- Pro demonstraci použijeme marker
    CreateThread(function()
        while sprays[sprayId] do
            local playerCoords = GetEntityCoords(PlayerPedId())
            local distance = #(playerCoords - sprayData.coords)
            
            if distance <= 50.0 then
                DrawMarker(
                    1, -- Typ markeru
                    sprayData.coords.x, sprayData.coords.y, sprayData.coords.z,
                    0.0, 0.0, 0.0,
                    sprayData.rotation.x, sprayData.rotation.y, sprayData.rotation.z,
                    1.0, 1.0, 0.1,
                    sprayData.color.r, sprayData.color.g, sprayData.color.b, sprayData.color.a,
                    false, false, 2, false, nil, nil, false
                )
                
                -- Interakce pro odstranění
                if distance <= 2.0 then
                    lib.showTextUI('[E] Odstranit spray')
                    if IsControlJustPressed(0, 38) then -- E
                        lib.hideTextUI()
                        TriggerServerEvent('valic_sprays:server:removeSpray', sprayId)
                    end
                elseif distance <= 3.0 then
                    lib.hideTextUI()
                end
            end
            
            Wait(0)
        end
    end)
end

-- Menu pro výběr sprayu
local function openSprayMenu()
    if isSprayMenuOpen then return end
    isSprayMenuOpen = true
    
    local options = {}
    
    for itemName, itemData in pairs(Config.SprayItems) do
        local hasItem = exports.ox_inventory:Search('count', itemName) > 0
        
        table.insert(options, {
            title = itemData.label,
            description = hasItem and 'Dostupný' or 'Nemáš',
            disabled = not hasItem,
            icon = 'spray-can',
            iconColor = hasItem and 'green' or 'red',
            onSelect = function()
                currentSprayItem = itemName
                openPatternMenu()
            end
        })
    end
    
    lib.registerContext({
        id = 'spray_menu',
        title = 'Výběr sprayu',
        options = options,
        onExit = function()
            isSprayMenuOpen = false
        end
    })
    
    lib.showContext('spray_menu')
end

-- Menu pro výběr vzoru
function openPatternMenu()
    local options = {}
    
    for i, pattern in pairs(Config.SprayPatterns) do
        table.insert(options, {
            title = 'Vzor ' .. i,
            description = pattern,
            icon = 'palette',
            onSelect = function()
                startSprayProcess(pattern)
            end
        })
    end
    
    lib.registerContext({
        id = 'pattern_menu',
        title = 'Výběr vzoru',
        options = options,
        onExit = function()
            isSprayMenuOpen = false
        end
    })
    
    lib.showContext('pattern_menu')
end

-- Proces sprayování
function startSprayProcess(pattern)
    isSprayMenuOpen = false
    
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)
    local heading = GetEntityHeading(ped)
    
    -- Kontrola zakázaných zón
    local restricted, zoneName = isInRestrictedZone(coords)
    if restricted then
        lib.notify({
            title = 'Spray System',
            description = 'Nemůžeš sprayovat v ' .. zoneName .. '!',
            type = 'error'
        })
        return
    end
    
    -- Kontrola povrchu
    if not isValidSurface(coords) then
        lib.notify({
            title = 'Spray System',
            description = 'Nemůžeš sprayovat na tento povrch!',
            type = 'error'
        })
        return
    end
    
    -- Raycast pro nalezení zeď
    local hit, hitCoords, surfaceNormal = lib.raycast.cam(511, 4, Config.SprayDistance)
    
    if not hit then
        lib.notify({
            title = 'Spray System',
            description = 'Musíš mířit na zeď!',
            type = 'error'
        })
        return
    end
    
    -- Animace sprayování
    lib.requestAnimDict('switch@franklin@lamar_tagging_wall')
    TaskPlayAnim(ped, 'switch@franklin@lamar_tagging_wall', 'lamar_tagging_wall_loop_lamar', 8.0, -8.0, -1, 49, 0, false, false, false)
    
    -- Progress bar
    if lib.progressBar({
        duration = Config.SprayDuration,
        label = 'Sprayuješ...',
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true
        }
    }) then
        -- Úspěšné dokončení
        ClearPedTasks(ped)
        
        local sprayData = Config.SprayItems[currentSprayItem]
        local rotation = vector3(0.0, 0.0, heading)
        
        TriggerServerEvent('valic_sprays:server:createSpray', hitCoords, rotation, pattern, sprayData.color, currentSprayItem)
    else
        -- Zrušeno
        ClearPedTasks(ped)
        lib.notify({
            title = 'Spray System',
            description = 'Sprayování zrušeno!',
            type = 'error'
        })
    end
end

-- Použití spray itemu
for itemName, _ in pairs(Config.SprayItems) do
    exports['qb-core']:CreateUseableItem(itemName, function(source, item)
        openSprayMenu()
    end)
end

-- Network eventy
RegisterNetEvent('valic_sprays:client:syncSpray', function(sprayId, sprayData)
    sprays[sprayId] = sprayData
    createSprayObject(sprayId, sprayData)
end)

RegisterNetEvent('valic_sprays:client:removeSpray', function(sprayId)
    sprays[sprayId] = nil
end)

RegisterNetEvent('valic_sprays:client:clearAllSprays', function()
    sprays = {}
end)

-- Příkazy pro otevření menu
RegisterCommand('spray', function()
    openSprayMenu()
end, false)

RegisterCommand('spreje', function()
    openSprayMenu()
end, false)
