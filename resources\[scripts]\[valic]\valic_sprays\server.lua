local QBCore = exports['qb-core']:GetCoreObject()

-- Načtení sprayů z databáze při startu
local sprays = {}

CreateThread(function()
    local result = MySQL.query.await('SELECT * FROM valic_sprays WHERE expires_at > NOW()')
    if result then
        for i = 1, #result do
            local spray = result[i]
            sprays[spray.id] = {
                id = spray.id,
                coords = json.decode(spray.coords),
                rotation = json.decode(spray.rotation),
                pattern = spray.pattern,
                color = json.decode(spray.color),
                creator = spray.creator,
                created_at = spray.created_at,
                expires_at = spray.expires_at
            }
        end
        print(('[valic_sprays] Načteno %d aktivních sprayů'):format(#result))
    end
end)

-- Event pro vytvoření sprayu
RegisterNetEvent('valic_sprays:server:createSpray', function(coords, rotation, pattern, color, sprayItem)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    
    if not Player then return end
    
    -- Kontrola itemu
    local item = Player.Functions.GetItemByName(sprayItem)
    if not item or item.amount < 1 then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Spray System',
            description = 'Nemáš spray!',
            type = 'error'
        })
        return
    end
    
    -- Kontrola limitu sprayů
    local playerSprays = MySQL.scalar.await('SELECT COUNT(*) FROM valic_sprays WHERE creator = ? AND expires_at > NOW()', {Player.PlayerData.citizenid})
    
    if playerSprays >= Config.MaxSpraysPerPlayer then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Spray System',
            description = 'Dosáhl jsi limitu sprayů (' .. Config.MaxSpraysPerPlayer .. ')!',
            type = 'error'
        })
        return
    end
    
    -- Odebrání itemu
    Player.Functions.RemoveItem(sprayItem, 1)
    TriggerClientEvent('inventory:client:ItemBox', src, QBCore.Shared.Items[sprayItem], 'remove', 1)
    
    -- Uložení do databáze
    local expiresAt = os.date('%Y-%m-%d %H:%M:%S', os.time() + (Config.SprayDecayTime / 1000))
    
    local sprayId = MySQL.insert.await([[
        INSERT INTO valic_sprays (coords, rotation, pattern, color, creator, expires_at) 
        VALUES (?, ?, ?, ?, ?, ?)
    ]], {
        json.encode(coords),
        json.encode(rotation),
        pattern,
        json.encode(color),
        Player.PlayerData.citizenid,
        expiresAt
    })
    
    if sprayId then
        -- Přidání do cache
        sprays[sprayId] = {
            id = sprayId,
            coords = coords,
            rotation = rotation,
            pattern = pattern,
            color = color,
            creator = Player.PlayerData.citizenid,
            created_at = os.date('%Y-%m-%d %H:%M:%S'),
            expires_at = expiresAt
        }
        
        -- Synchronizace s klienty
        TriggerClientEvent('valic_sprays:client:syncSpray', -1, sprayId, sprays[sprayId])
        
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Spray System',
            description = 'Spray úspěšně vytvořen!',
            type = 'success'
        })
    end
end)

-- Event pro odstranění sprayu
RegisterNetEvent('valic_sprays:server:removeSpray', function(sprayId)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    
    if not Player then return end
    
    local spray = sprays[sprayId]
    if not spray then return end
    
    -- Kontrola oprávnění (pouze tvůrce nebo admin)
    if spray.creator ~= Player.PlayerData.citizenid and not QBCore.Functions.HasPermission(src, 'admin') then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Spray System',
            description = 'Nemáš oprávnění odstranit tento spray!',
            type = 'error'
        })
        return
    end
    
    -- Odstranění z databáze
    MySQL.query('DELETE FROM valic_sprays WHERE id = ?', {sprayId})
    
    -- Odstranění z cache
    sprays[sprayId] = nil
    
    -- Synchronizace s klienty
    TriggerClientEvent('valic_sprays:client:removeSpray', -1, sprayId)
    
    TriggerClientEvent('ox_lib:notify', src, {
        title = 'Spray System',
        description = 'Spray odstraněn!',
        type = 'success'
    })
end)

-- Callback pro načtení sprayů
QBCore.Functions.CreateCallback('valic_sprays:server:getSprays', function(source, cb)
    cb(sprays)
end)

-- Automatické čištění expirovaných sprayů
CreateThread(function()
    while true do
        Wait(60000) -- Každou minutu
        
        local expiredSprays = MySQL.query.await('SELECT id FROM valic_sprays WHERE expires_at <= NOW()')
        
        if expiredSprays and #expiredSprays > 0 then
            for i = 1, #expiredSprays do
                local sprayId = expiredSprays[i].id
                sprays[sprayId] = nil
                TriggerClientEvent('valic_sprays:client:removeSpray', -1, sprayId)
            end
            
            MySQL.query('DELETE FROM valic_sprays WHERE expires_at <= NOW()')
            print(('[valic_sprays] Odstraněno %d expirovaných sprayů'):format(#expiredSprays))
        end
    end
end)

-- Admin příkazy
QBCore.Commands.Add('clearsprays', 'Vymaže všechny spraye (Admin)', {}, false, function(source, args)
    local src = source
    
    if not QBCore.Functions.HasPermission(src, 'admin') then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Spray System',
            description = 'Nemáš oprávnění!',
            type = 'error'
        })
        return
    end
    
    MySQL.query('DELETE FROM valic_sprays')
    sprays = {}
    TriggerClientEvent('valic_sprays:client:clearAllSprays', -1)
    
    TriggerClientEvent('ox_lib:notify', src, {
        title = 'Spray System',
        description = 'Všechny spraye byly vymazány!',
        type = 'success'
    })
end, 'admin')

QBCore.Commands.Add('sprayinfo', 'Zobrazí informace o sprayích', {}, false, function(source, args)
    local src = source
    local totalSprays = 0
    for _ in pairs(sprays) do
        totalSprays = totalSprays + 1
    end
    
    TriggerClientEvent('ox_lib:notify', src, {
        title = 'Spray System',
        description = 'Aktivních sprayů: ' .. totalSprays,
        type = 'inform'
    })
end)
