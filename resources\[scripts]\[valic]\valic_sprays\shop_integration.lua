-- Integrace pro qb-shops nebo jiné shop systémy
-- <PERSON><PERSON><PERSON>j tyto itemy do svého shop systému

-- Pro qb-shops (products tabulka)
local shopItems = {
    {
        name = 'spray_red',
        price = 25,
        amount = 50,
        info = {},
        type = 'item',
        slot = 1,
    },
    {
        name = 'spray_blue', 
        price = 25,
        amount = 50,
        info = {},
        type = 'item',
        slot = 2,
    },
    {
        name = 'spray_green',
        price = 25,
        amount = 50,
        info = {},
        type = 'item',
        slot = 3,
    },
    {
        name = 'spray_yellow',
        price = 25,
        amount = 50,
        info = {},
        type = 'item',
        slot = 4,
    },
    {
        name = 'spray_black',
        price = 25,
        amount = 50,
        info = {},
        type = 'item',
        slot = 5,
    },
    {
        name = 'spray_white',
        price = 25,
        amount = 50,
        info = {},
        type = 'item',
        slot = 6,
    }
}

-- SQL pro přidání do qb-shops
--[[
INSERT INTO `shops` (`name`, `label`, `coords`, `ped`, `scenario`, `radius`, `targetIcon`, `targetLabel`, `products`, `showblip`, `blipsprite`, `blipscale`, `blipcolor`) VALUES
('graffiti_shop', 'Graffiti Shop', '{"x": -1155.35, "y": -1425.68, "z": 4.95, "w": 123.0}', 'a_m_y_hipster_01', 'WORLD_HUMAN_CLIPBOARD', 2.0, 'fas fa-spray-can', 'Graffiti Shop', '[
    {"name": "spray_red", "price": 25, "amount": 50, "info": {}, "type": "item", "slot": 1},
    {"name": "spray_blue", "price": 25, "amount": 50, "info": {}, "type": "item", "slot": 2},
    {"name": "spray_green", "price": 25, "amount": 50, "info": {}, "type": "item", "slot": 3},
    {"name": "spray_yellow", "price": 25, "amount": 50, "info": {}, "type": "item", "slot": 4},
    {"name": "spray_black", "price": 25, "amount": 50, "info": {}, "type": "item", "slot": 5},
    {"name": "spray_white", "price": 25, "amount": 50, "info": {}, "type": "item", "slot": 6}
]', 1, 110, 0.6, 2);
--]]

-- Pro ox_inventory shops
--[[
Přidej do ox_inventory/data/shops.lua:

['graffiti'] = {
    name = 'Graffiti Shop',
    blip = {
        id = 110, colour = 2, scale = 0.6
    },
    inventory = {
        { name = 'spray_red', price = 25 },
        { name = 'spray_blue', price = 25 },
        { name = 'spray_green', price = 25 },
        { name = 'spray_yellow', price = 25 },
        { name = 'spray_black', price = 25 },
        { name = 'spray_white', price = 25 }
    },
    locations = {
        vec3(-1155.35, -1425.68, 4.95)
    },
    targets = {
        { ped = `a_m_y_hipster_01` }
    }
}
--]]

-- Admin příkaz pro dávání sprayů
RegisterCommand('givespray', function(source, args)
    local src = source
    local Player = QBCore.Functions.GetPlayer(src)
    
    if not QBCore.Functions.HasPermission(src, 'admin') then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Spray System',
            description = 'Nemáš oprávnění!',
            type = 'error'
        })
        return
    end
    
    if not args[1] or not args[2] then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Spray System',
            description = 'Použití: /givespray [id] [barva]',
            type = 'error'
        })
        return
    end
    
    local targetId = tonumber(args[1])
    local sprayColor = args[2]
    local sprayItem = 'spray_' .. sprayColor
    
    if not Config.SprayItems[sprayItem] then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Spray System',
            description = 'Neplatná barva! (red, blue, green, yellow, black, white)',
            type = 'error'
        })
        return
    end
    
    local TargetPlayer = QBCore.Functions.GetPlayer(targetId)
    if not TargetPlayer then
        TriggerClientEvent('ox_lib:notify', src, {
            title = 'Spray System',
            description = 'Hráč není online!',
            type = 'error'
        })
        return
    end
    
    TargetPlayer.Functions.AddItem(sprayItem, 1)
    TriggerClientEvent('inventory:client:ItemBox', targetId, QBCore.Shared.Items[sprayItem], 'add', 1)
    
    TriggerClientEvent('ox_lib:notify', src, {
        title = 'Spray System',
        description = 'Spray dán hráči!',
        type = 'success'
    })
    
    TriggerClientEvent('ox_lib:notify', targetId, {
        title = 'Spray System',
        description = 'Dostal jsi spray od admina!',
        type = 'success'
    })
end, 'admin')
