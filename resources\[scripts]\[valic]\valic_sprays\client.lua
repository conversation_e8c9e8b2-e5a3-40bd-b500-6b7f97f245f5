local QBCore = exports['qb-core']:GetCoreObject()

-- Lokální proměnné
local sprays = {}
local isSprayMenuOpen = false
local currentSprayItem = nil
local isPositioning = false
local previewSpray = {
    coords = vector3(0, 0, 0),
    rotation = vector3(0, 0, 0),
    size = 1.0,
    text = '',
    color = {r = 255, g = 255, b = 255, a = 255},
    font = 4
}

-- Načtení sprayů př<PERSON> př<PERSON>í
CreateThread(function()
    while not QBCore.Functions.GetPlayerData().citizenid do
        Wait(100)
    end
    
    QBCore.Functions.TriggerCallback('valic_sprays:server:getSprays', function(serverSprays)
        sprays = serverSprays
        for sprayId, sprayData in pairs(sprays) do
            createSprayObject(sprayId, sprayData)
        end
    end)
end)

-- Funkce pro kontrolu zakázaných zón
local function isInRestrictedZone(coords)
    for _, zone in pairs(Config.RestrictedZones) do
        local distance = #(coords - zone.coords)
        if distance <= zone.radius then
            return true, zone.name
        end
    end
    return false
end

-- <PERSON>ce pro input textu
local function getTextInput()
    local fontOptions = {}
    for fontId, fontName in pairs(Config.TextSettings.fonts) do
        table.insert(fontOptions, {
            value = fontId,
            label = fontName
        })
    end

    local input = lib.inputDialog('Spray Text', {
        {
            type = 'input',
            label = 'Text sprayu',
            description = 'Napiš text pro svůj spray (max ' .. Config.TextSettings.maxLength .. ' znaků)',
            required = true,
            max = Config.TextSettings.maxLength
        },
        {
            type = 'select',
            label = 'Font',
            description = 'Vyber font pro text',
            required = true,
            default = 4,
            options = fontOptions
        }
    })

    return input
end

-- Funkce pro vykreslení preview sprayu
local function drawPreviewSpray()
    if not isPositioning then return end

    local coords = previewSpray.coords
    local size = previewSpray.size
    local color = previewSpray.color

    -- Vykreslení textu ve 3D prostoru
    SetTextFont(previewSpray.font)
    SetTextProportional(1)
    SetTextScale(size, size)
    SetTextColour(color.r, color.g, color.b, color.a)
    SetTextDropshadow(1, 0, 0, 0, 255)
    SetTextEdge(1, 0, 0, 0, 255)
    SetTextDropShadow()
    SetTextOutline()
    SetTextEntry("STRING")
    AddTextComponentString(previewSpray.text)
    SetDrawOrigin(coords.x, coords.y, coords.z, 0)
    DrawText(0.0, 0.0)
    ClearDrawOrigin()

    -- Vykreslení bounding boxu
    DrawMarker(
        1, -- Typ markeru
        coords.x, coords.y, coords.z,
        0.0, 0.0, 0.0,
        previewSpray.rotation.x, previewSpray.rotation.y, previewSpray.rotation.z,
        size * 2, size * 0.5, 0.1,
        color.r, color.g, color.b, 100,
        false, false, 2, false, nil, nil, false
    )
end

-- Funkce pro pozicování sprayu
local function startPositioning(text, font, color)
    if isPositioning then return end

    isPositioning = true
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)
    local heading = GetEntityHeading(ped)

    -- Raycast pro nalezení zeď
    local hit, hitCoords, surfaceNormal = lib.raycast.cam(511, 4, 10.0)

    if not hit then
        lib.notify({
            title = 'Spray System',
            description = 'Musíš mířit na povrch!',
            type = 'error'
        })
        isPositioning = false
        return
    end

    -- Nastavení preview sprayu
    previewSpray.coords = hitCoords
    previewSpray.rotation = vector3(0.0, 0.0, heading)
    previewSpray.size = Config.TextSettings.defaultSize
    previewSpray.text = text
    previewSpray.color = color
    previewSpray.font = font

    -- Instrukce pro hráče
    lib.notify({
        title = 'Pozicování sprayu',
        description = 'Šipky: pohyb | Num+/-: velikost | A/D: rotace | E: potvrdit | Backspace: zrušit',
        type = 'inform',
        duration = 8000
    })

    -- Loop pro pozicování
    CreateThread(function()
        while isPositioning do
            drawPreviewSpray()

            -- Ovládání
            if IsControlJustPressed(0, Config.Controls.moveUp) then
                previewSpray.coords = previewSpray.coords + vector3(0.0, 0.0, 0.05)
            elseif IsControlJustPressed(0, Config.Controls.moveDown) then
                previewSpray.coords = previewSpray.coords - vector3(0.0, 0.0, 0.05)
            elseif IsControlJustPressed(0, Config.Controls.moveLeft) then
                local heading = GetEntityHeading(ped)
                local offset = vector3(math.sin(math.rad(heading)) * 0.05, -math.cos(math.rad(heading)) * 0.05, 0.0)
                previewSpray.coords = previewSpray.coords + offset
            elseif IsControlJustPressed(0, Config.Controls.moveRight) then
                local heading = GetEntityHeading(ped)
                local offset = vector3(-math.sin(math.rad(heading)) * 0.05, math.cos(math.rad(heading)) * 0.05, 0.0)
                previewSpray.coords = previewSpray.coords + offset
            elseif IsControlJustPressed(0, Config.Controls.increaseSize) then
                previewSpray.size = math.min(previewSpray.size + 0.1, Config.TextSettings.maxSize)
            elseif IsControlJustPressed(0, Config.Controls.decreaseSize) then
                previewSpray.size = math.max(previewSpray.size - 0.1, Config.TextSettings.minSize)
            elseif IsControlJustPressed(0, Config.Controls.rotateLeft) then
                previewSpray.rotation = previewSpray.rotation + vector3(0.0, 0.0, 5.0)
            elseif IsControlJustPressed(0, Config.Controls.rotateRight) then
                previewSpray.rotation = previewSpray.rotation - vector3(0.0, 0.0, 5.0)
            elseif IsControlJustPressed(0, Config.Controls.confirm) then
                -- Potvrzení sprayu
                confirmSpray()
                break
            elseif IsControlJustPressed(0, Config.Controls.cancel) then
                -- Zrušení
                isPositioning = false
                lib.notify({
                    title = 'Spray System',
                    description = 'Pozicování zrušeno!',
                    type = 'error'
                })
                break
            end

            Wait(0)
        end
    end)
end

-- Funkce pro potvrzení sprayu
function confirmSpray()
    isPositioning = false

    local ped = PlayerPedId()

    -- Animace sprayování
    lib.requestAnimDict('switch@franklin@lamar_tagging_wall')
    TaskPlayAnim(ped, 'switch@franklin@lamar_tagging_wall', 'lamar_tagging_wall_loop_lamar', 8.0, -8.0, -1, 49, 0, false, false, false)

    -- Progress bar
    if lib.progressBar({
        duration = Config.SprayDuration,
        label = 'Sprayuješ...',
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true
        }
    }) then
        -- Úspěšné dokončení
        ClearPedTasks(ped)

        TriggerServerEvent('valic_sprays:server:createSpray',
            previewSpray.coords,
            previewSpray.rotation,
            previewSpray.text,
            previewSpray.color,
            currentSprayItem,
            previewSpray.size,
            previewSpray.font
        )
    else
        -- Zrušeno
        ClearPedTasks(ped)
        lib.notify({
            title = 'Spray System',
            description = 'Sprayování zrušeno!',
            type = 'error'
        })
    end
end

-- Funkce pro vytvoření spray objektu
function createSprayObject(sprayId, sprayData)
    CreateThread(function()
        while sprays[sprayId] do
            local playerCoords = GetEntityCoords(PlayerPedId())
            local distance = #(playerCoords - sprayData.coords)

            if distance <= 50.0 then
                -- Vykreslení textu
                SetTextFont(sprayData.font or 4)
                SetTextProportional(1)
                SetTextScale(sprayData.size or 1.0, sprayData.size or 1.0)
                SetTextColour(sprayData.color.r, sprayData.color.g, sprayData.color.b, sprayData.color.a)
                SetTextDropshadow(1, 0, 0, 0, 255)
                SetTextEdge(1, 0, 0, 0, 255)
                SetTextDropShadow()
                SetTextOutline()
                SetTextEntry("STRING")
                AddTextComponentString(sprayData.text or sprayData.pattern)
                SetDrawOrigin(sprayData.coords.x, sprayData.coords.y, sprayData.coords.z, 0)
                DrawText(0.0, 0.0)
                ClearDrawOrigin()

                -- Interakce pro odstranění
                if distance <= 2.0 then
                    lib.showTextUI('[E] Odstranit spray')
                    if IsControlJustPressed(0, 38) then -- E
                        lib.hideTextUI()
                        TriggerServerEvent('valic_sprays:server:removeSpray', sprayId)
                    end
                elseif distance <= 3.0 then
                    lib.hideTextUI()
                end
            end

            Wait(0)
        end
    end)
end

-- Menu pro výběr sprayu
local function openSprayMenu()
    if isSprayMenuOpen then return end
    isSprayMenuOpen = true

    local options = {}

    for itemName, itemData in pairs(Config.SprayItems) do
        local hasItem = exports.ox_inventory:Search('count', itemName) > 0

        table.insert(options, {
            title = itemData.label,
            description = hasItem and 'Dostupný' or 'Nemáš',
            disabled = not hasItem,
            icon = 'spray-can',
            iconColor = hasItem and 'green' or 'red',
            onSelect = function()
                currentSprayItem = itemName
                startTextInput()
            end
        })
    end

    lib.registerContext({
        id = 'spray_menu',
        title = 'Výběr sprayu',
        options = options,
        onExit = function()
            isSprayMenuOpen = false
        end
    })

    lib.showContext('spray_menu')
end

-- Funkce pro zadání textu
function startTextInput()
    isSprayMenuOpen = false

    local input = getTextInput()

    if not input or not input[1] or input[1] == '' then
        lib.notify({
            title = 'Spray System',
            description = 'Musíš zadat text!',
            type = 'error'
        })
        return
    end

    local text = input[1]
    local font = input[2] or 4
    local sprayData = Config.SprayItems[currentSprayItem]

    -- Spustit pozicování
    startPositioning(text, font, sprayData.color)
end



-- Export funkce pro ox_inventory
exports('useSpray', function(data, slot)
    local itemName = data.name
    if Config.SprayItems[itemName] then
        currentSprayItem = itemName
        openSprayMenu()
    end
end)

-- Event pro použití spray itemu
RegisterNetEvent('valic_sprays:client:useSpray', function(itemName)
    if Config.SprayItems[itemName] then
        currentSprayItem = itemName
        openSprayMenu()
    end
end)

-- Network eventy
RegisterNetEvent('valic_sprays:client:syncSpray', function(sprayId, sprayData)
    sprays[sprayId] = sprayData
    createSprayObject(sprayId, sprayData)
end)

RegisterNetEvent('valic_sprays:client:removeSpray', function(sprayId)
    sprays[sprayId] = nil
end)

RegisterNetEvent('valic_sprays:client:clearAllSprays', function()
    sprays = {}
end)

-- Příkazy pro otevření menu
RegisterCommand('spray', function()
    openSprayMenu()
end, false)

RegisterCommand('spreje', function()
    openSprayMenu()
end, false)
