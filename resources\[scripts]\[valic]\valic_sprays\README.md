# Valic Sprays - FiveM QBCore Spray System

Kompletní spray systém pro FiveM QBCore s podporou ox_lib a ox_inventory.

## Funkce

- 🎨 **6 různých barev sprayů** (červená, modrá, zelená, žlutá, černá, bílá)
- ✍️ **Vlastní texty** - nap<PERSON><PERSON> si co chceš
- 📏 **Nastavitelná velikost** sprayu (0.3 - 2.0)
- 🎭 **5 různých fontů** pro text
- 🎯 **Pokročilé pozicování** - pohyb, rotace, velikost
- 👁️ **Live náhled** před potvrzením
- 🚫 **Zaká<PERSON><PERSON> zóny** (LSPD, nemocnice, banka)
- ⏰ **<PERSON>k<PERSON> mizen<PERSON>** sprayů po 24 hodinách
- 📊 **Limit sprayů** na hráče (10 sprayů)
- 🗄️ **Databázové uložení** přes oxmysql
- 🎮 **ox_lib menu** a notifikace
- 👮 **<PERSON><PERSON> p<PERSON>y** pro správu

## Instalace

### 1. Databáze
Spusť SQL příkazy z `install.sql` ve své databázi.

### 2. Ox_inventory itemy
Zkopíruj obsah `ox_inventory_items.lua` do `ox_inventory/data/items.lua`

### 3. Obrázky itemů
Přidej tyto obrázky do `ox_inventory/web/images/`:
- spray_red.png
- spray_blue.png
- spray_green.png
- spray_yellow.png
- spray_black.png
- spray_white.png

### 4. Server.cfg
Přidej do server.cfg:
```
ensure valic_sprays
```

### 5. Dependencies
Ujisti se, že máš tyto zdroje:
- qb-core
- ox_lib
- ox_inventory
- oxmysql

## Použití

### Pro hráče
- **/spray** nebo **/spreje** - Otevře spray menu
- **E** - Odstraní spray (pouze vlastní)
- **Použij spray item** z inventáře - automaticky otevře menu

### Ovládání při pozicování
- **Šipky** - Pohyb sprayu (nahoru/dolů/vlevo/vpravo)
- **Num +/-** - Zvětšení/zmenšení velikosti
- **A/D** - Rotace sprayu vlevo/vpravo
- **E** - Potvrzení a sprayování
- **Backspace** - Zrušení pozicování

### Admin příkazy
- `/clearsprays` - Vymaže všechny spraye
- `/sprayinfo` - Zobrazí počet aktivních sprayů

## Konfigurace

Všechna nastavení najdeš v `config.lua`:

- `MaxSpraysPerPlayer` - Limit sprayů na hráče
- `SprayDistance` - Vzdálenost pro sprayování
- `SprayDuration` - Doba sprayování
- `SprayDecayTime` - Doba do zmizení sprayu
- `RestrictedZones` - Zakázané zóny
- `SprayPatterns` - Dostupné vzory

## Jak to funguje

1. Hráč použije spray item nebo příkaz /spray
2. Vybere barvu sprayu (musí mít item)
3. Napíše vlastní text a vybere font
4. Míří na povrch a spustí se pozicování
5. Pomocí kláves upraví pozici, velikost a rotaci
6. Potvrdí pozici klávesou E
7. Sprayuje s progress barem
8. Spray se uloží do databáze a zobrazí všem hráčům
9. Po 24 hodinách se automaticky smaže

## Technické detaily

- **Server-side**: Databázová logika, validace, synchronizace
- **Client-side**: Menu, animace, raycast, 3D objekty
- **Database**: MySQL tabulky pro spraye, statistiky, reporty
- **Performance**: Optimalizované pro více hráčů

## Podpora

Pro podporu nebo úpravy kontaktuj Valic.

---

**Verze**: 1.0.0  
**Autor**: Valic  
**Framework**: QBCore  
**Dependencies**: ox_lib, ox_inventory, oxmysql
